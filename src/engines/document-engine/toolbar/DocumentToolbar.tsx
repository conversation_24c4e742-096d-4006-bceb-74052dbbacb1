import React, { useState } from 'react';
import { Editor } from '@tiptap/react';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Separator } from '@/components/ui/separator';
import {
  Bold,
  Italic,
  Underline as UnderlineIcon,
  AlignLeft,
  AlignCenter,
  AlignRight,
  AlignJustify,
  List,
  ListOrdered,
  Heading1,
  Heading2,
  Heading3,
  Download,
  Save,
  Undo,
  Redo,
  Table as TableIcon,
  Highlighter,
  Type,
  Palette,
  FileText,
  Hash,
  Link,
  Quote,
  MoreHorizontal
} from 'lucide-react';
import { ToolbarConfig, DocumentTheme, ExportOptions } from '../core/DocumentTypes';

interface DocumentToolbarProps {
  editor: Editor | null;
  config?: ToolbarConfig;
  theme: DocumentTheme;
  onExport: (options: ExportOptions) => void;
  onSave: () => void;
  isExporting?: boolean;
  isLoading?: boolean;
}

export const DocumentToolbar: React.FC<DocumentToolbarProps> = ({
  editor,
  config,
  theme,
  onExport,
  onSave,
  isExporting = false,
  isLoading = false
}) => {
  const [selectedFont, setSelectedFont] = useState(theme.fontFamily || 'Times New Roman');
  const [selectedFontSize, setSelectedFontSize] = useState(theme.fontSize || '12pt');

  if (!editor) return null;

  const isActive = (name: string, attributes?: Record<string, any>) => {
    return editor.isActive(name, attributes);
  };

  const canUndo = editor.can().undo();
  const canRedo = editor.can().redo();

  // Font families for legal documents
  const fontFamilies = [
    'Times New Roman',
    'Arial',
    'Calibri',
    'Georgia',
    'Garamond',
    'Book Antiqua'
  ];

  // Font sizes
  const fontSizes = [
    '8pt', '9pt', '10pt', '11pt', '12pt', '14pt', '16pt', '18pt', '20pt', '24pt'
  ];

  // Handle font family change
  const handleFontChange = (font: string) => {
    setSelectedFont(font);
    editor.chain().focus().setFontFamily(font).run();
  };

  // Handle font size change
  const handleFontSizeChange = (size: string) => {
    setSelectedFontSize(size);
    // Apply font size via CSS custom property or direct styling
    const numericSize = parseInt(size);
    editor.chain().focus().setMark('textStyle', { fontSize: `${numericSize}pt` }).run();
  };

  // Handle export with different formats
  const handleExport = (format: 'pdf' | 'docx' | 'html') => {
    onExport({
      format,
      pageSize: 'Letter',
      orientation: 'portrait',
      includeComments: false,
      includeMetadata: true
    });
  };

  // Insert table
  const insertTable = () => {
    editor.chain().focus().insertTable({ rows: 3, cols: 3, withHeaderRow: true }).run();
  };

  // Insert legal clause
  const insertLegalClause = (type: string) => {
    const templates = {
      whereas: 'WHEREAS, [insert recital text];',
      definition: '"[Term]" means [definition];',
      covenant: 'The [Party] covenants and agrees that [covenant text];',
      condition: 'This Agreement is subject to the condition that [condition text];'
    };
    
    const template = templates[type as keyof typeof templates] || '';
    editor.chain().focus().insertContent(`<p data-clause-type="${type}">${template}</p>`).run();
  };

  // Auto-number sections
  const updateNumbering = () => {
    // TODO: Implement custom numbering logic
    console.log('Auto-numbering feature to be implemented');
  };

  return (
    <div className="document-toolbar border-b border-slate-200 dark:border-slate-700 bg-white dark:bg-slate-950 p-2">
      <div className="flex flex-wrap items-center gap-1">
        
        {/* File Operations */}
        <div className="flex items-center gap-1 mr-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={onSave}
            disabled={isLoading}
            className="h-8 px-2"
          >
            <Save className="h-4 w-4 mr-1" />
            Save
          </Button>
          
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                disabled={isExporting}
                className="h-8 px-2"
              >
                <Download className="h-4 w-4 mr-1" />
                Export
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuItem onClick={() => handleExport('pdf')}>
                <FileText className="h-4 w-4 mr-2" />
                Export as PDF
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleExport('docx')}>
                <FileText className="h-4 w-4 mr-2" />
                Export as DOCX
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleExport('html')}>
                <FileText className="h-4 w-4 mr-2" />
                Export as HTML
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        <Separator orientation="vertical" className="h-6 mx-1" />

        {/* Undo/Redo */}
        <div className="flex items-center gap-1 mr-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => editor.chain().focus().undo().run()}
            disabled={!canUndo}
            className="h-8 w-8 p-0"
          >
            <Undo className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => editor.chain().focus().redo().run()}
            disabled={!canRedo}
            className="h-8 w-8 p-0"
          >
            <Redo className="h-4 w-4" />
          </Button>
        </div>

        <Separator orientation="vertical" className="h-6 mx-1" />

        {/* Font Controls */}
        <div className="flex items-center gap-1 mr-2">
          <Select value={selectedFont} onValueChange={handleFontChange}>
            <SelectTrigger className="w-32 h-8">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {fontFamilies.map(font => (
                <SelectItem key={font} value={font} style={{ fontFamily: font }}>
                  {font}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Select value={selectedFontSize} onValueChange={handleFontSizeChange}>
            <SelectTrigger className="w-16 h-8">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {fontSizes.map(size => (
                <SelectItem key={size} value={size}>
                  {size}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <Separator orientation="vertical" className="h-6 mx-1" />

        {/* Text Formatting */}
        <div className="flex items-center gap-1 mr-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => editor.chain().focus().toggleBold().run()}
            data-active={isActive('bold')}
            className="h-8 w-8 p-0 data-[active=true]:bg-slate-200 dark:data-[active=true]:bg-slate-700"
          >
            <Bold className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => editor.chain().focus().toggleItalic().run()}
            data-active={isActive('italic')}
            className="h-8 w-8 p-0 data-[active=true]:bg-slate-200 dark:data-[active=true]:bg-slate-700"
          >
            <Italic className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => editor.chain().focus().toggleUnderline().run()}
            data-active={isActive('underline')}
            className="h-8 w-8 p-0 data-[active=true]:bg-slate-200 dark:data-[active=true]:bg-slate-700"
          >
            <UnderlineIcon className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => editor.chain().focus().toggleHighlight().run()}
            data-active={isActive('highlight')}
            className="h-8 w-8 p-0 data-[active=true]:bg-slate-200 dark:data-[active=true]:bg-slate-700"
          >
            <Highlighter className="h-4 w-4" />
          </Button>
        </div>

        <Separator orientation="vertical" className="h-6 mx-1" />

        {/* Headings */}
        <div className="flex items-center gap-1 mr-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => editor.chain().focus().toggleHeading({ level: 1 }).run()}
            data-active={isActive('heading', { level: 1 })}
            className="h-8 w-8 p-0 data-[active=true]:bg-slate-200 dark:data-[active=true]:bg-slate-700"
          >
            <Heading1 className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => editor.chain().focus().toggleHeading({ level: 2 }).run()}
            data-active={isActive('heading', { level: 2 })}
            className="h-8 w-8 p-0 data-[active=true]:bg-slate-200 dark:data-[active=true]:bg-slate-700"
          >
            <Heading2 className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => editor.chain().focus().toggleHeading({ level: 3 }).run()}
            data-active={isActive('heading', { level: 3 })}
            className="h-8 w-8 p-0 data-[active=true]:bg-slate-200 dark:data-[active=true]:bg-slate-700"
          >
            <Heading3 className="h-4 w-4" />
          </Button>
        </div>

        <Separator orientation="vertical" className="h-6 mx-1" />

        {/* Alignment */}
        <div className="flex items-center gap-1 mr-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => editor.chain().focus().setTextAlign('left').run()}
            data-active={isActive('textAlign', { textAlign: 'left' })}
            className="h-8 w-8 p-0 data-[active=true]:bg-slate-200 dark:data-[active=true]:bg-slate-700"
          >
            <AlignLeft className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => editor.chain().focus().setTextAlign('center').run()}
            data-active={isActive('textAlign', { textAlign: 'center' })}
            className="h-8 w-8 p-0 data-[active=true]:bg-slate-200 dark:data-[active=true]:bg-slate-700"
          >
            <AlignCenter className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => editor.chain().focus().setTextAlign('right').run()}
            data-active={isActive('textAlign', { textAlign: 'right' })}
            className="h-8 w-8 p-0 data-[active=true]:bg-slate-200 dark:data-[active=true]:bg-slate-700"
          >
            <AlignRight className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => editor.chain().focus().setTextAlign('justify').run()}
            data-active={isActive('textAlign', { textAlign: 'justify' })}
            className="h-8 w-8 p-0 data-[active=true]:bg-slate-200 dark:data-[active=true]:bg-slate-700"
          >
            <AlignJustify className="h-4 w-4" />
          </Button>
        </div>

        <Separator orientation="vertical" className="h-6 mx-1" />

        {/* Lists and Tables */}
        <div className="flex items-center gap-1 mr-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => editor.chain().focus().toggleBulletList().run()}
            data-active={isActive('bulletList')}
            className="h-8 w-8 p-0 data-[active=true]:bg-slate-200 dark:data-[active=true]:bg-slate-700"
          >
            <List className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => editor.chain().focus().toggleOrderedList().run()}
            data-active={isActive('orderedList')}
            className="h-8 w-8 p-0 data-[active=true]:bg-slate-200 dark:data-[active=true]:bg-slate-700"
          >
            <ListOrdered className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={insertTable}
            className="h-8 w-8 p-0"
          >
            <TableIcon className="h-4 w-4" />
          </Button>
        </div>

        <Separator orientation="vertical" className="h-6 mx-1" />

        {/* Legal Document Features */}
        <div className="flex items-center gap-1">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="h-8 px-2">
                <Quote className="h-4 w-4 mr-1" />
                Legal
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuItem onClick={() => insertLegalClause('whereas')}>
                Insert WHEREAS Clause
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => insertLegalClause('definition')}>
                Insert Definition
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => insertLegalClause('covenant')}>
                Insert Covenant
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => insertLegalClause('condition')}>
                Insert Condition
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={updateNumbering}>
                <Hash className="h-4 w-4 mr-2" />
                Update Numbering
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </div>
  );
};

export default DocumentToolbar;
