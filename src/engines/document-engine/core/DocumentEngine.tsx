import React, { useRef, useState, useMemo, useCallback } from 'react';
import { useEditor, EditorContent } from '@tiptap/react';
import {
  DocumentEngineConfig,
  DocumentEngineEvents,
  DocumentTheme,
  ExportOptions,
  DocumentData
} from './DocumentTypes';
import { useDocumentEngine } from '../hooks/useDocumentEngine';
import DocumentToolbar from '../toolbar/DocumentToolbar';
import DocumentExporter from '../export/DocumentExporter';
import CollaborationManager from '../collaboration/CollaborationManager';
import { createTipTapExtensions } from '../editor/TipTapExtensions';
import { DocumentEngineErrorBoundary, useDocumentEngineErrorHandler } from '../components/DocumentEngineErrorBoundary';
import { DocumentEngineLoadingState } from '../components/DocumentEngineLoader';

export interface DocumentEngineProps extends DocumentEngineEvents {
  // Core props
  initialContent?: string;
  config?: DocumentEngineConfig;
  className?: string;
  
  // Content management
  template?: any;
  readOnly?: boolean;
  
  // Styling
  theme?: DocumentTheme;
  
  // Integration props (for wizard mode)
  wizardData?: any;
  onWizardDataChange?: (data: any) => void;
}

/**
 * Standalone Document Engine Component
 * 
 * A powerful, extensible document editing engine that can operate in multiple modes:
 * - wizard: Integrated with contract wizard for form-driven document generation
 * - standalone: Independent document editor
 * - collaborative: Real-time collaborative editing (future)
 * 
 * Features:
 * - Advanced TipTap editor with legal document extensions
 * - Professional document styling and formatting
 * - Export capabilities (PDF, DOCX, HTML)
 * - Extensible toolbar system
 * - Foundation for real-time collaboration
 */
export const DocumentEngine: React.FC<DocumentEngineProps> = ({
  initialContent = '',
  config = { mode: 'standalone' },
  className = '',
  template,
  readOnly = false,
  theme,
  wizardData,
  onWizardDataChange,
  onContentChange,
  onStructureChange,
  onSave,
  onExport,
  onError,
  ...collaborationEvents
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [isExporting, setIsExporting] = useState(false);
  const [isInitializing, setIsInitializing] = useState(true);
  const [operationInProgress, setOperationInProgress] = useState<{
    type: 'saving' | 'exporting' | 'syncing' | null;
    progress?: number;
  }>({ type: null });

  // Use error handler hook
  const { error: engineError, handleError: handleEngineError, clearError } = useDocumentEngineErrorHandler();
  
  // Default theme configuration
  const defaultTheme: DocumentTheme = {
    fontFamily: 'Times New Roman',
    fontSize: '12pt',
    lineHeight: 1.6,
    pageWidth: '8.5in',
    margins: {
      top: '1in',
      right: '1in',
      bottom: '1in',
      left: '1in'
    },
    colors: {
      text: '#1a1a1a',
      background: '#ffffff',
      border: '#e5e7eb',
      accent: '#3b82f6'
    }
  };

  const appliedTheme = { ...defaultTheme, ...theme };

  // Create TipTap extensions based on configuration
  const extensions = useMemo(() => {
    try {
      console.log('Creating TipTap extensions with config:', config);
      const exts = createTipTapExtensions({
        mode: config.mode,
        collaboration: config.collaboration,
        permissions: config.permissions
      });
      console.log('TipTap extensions created successfully:', exts.length, 'extensions');
      return exts;
    } catch (error) {
      console.error('Error creating TipTap extensions:', error);
      // Return minimal extensions as fallback
      return [];
    }
  }, [config]);

  // Initialize TipTap editor with error handling
  const editor = useEditor({
    extensions,
    content: initialContent || '<p>Start typing your document...</p>',
    editable: !readOnly,
    editorProps: {
      attributes: {
        class: 'focus:outline-none document-content',
        style: `
          font-family: "${appliedTheme.fontFamily}", serif;
          font-size: ${appliedTheme.fontSize};
          line-height: ${appliedTheme.lineHeight};
          color: ${appliedTheme.colors.text};
          background: ${appliedTheme.colors.background};
          min-height: 11in;
          padding: ${appliedTheme.margins.top} ${appliedTheme.margins.right} ${appliedTheme.margins.bottom} ${appliedTheme.margins.left};
          max-width: ${appliedTheme.pageWidth};
          margin: 0 auto;
          box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
          border-radius: 4px;
        `
      }
    },
    onCreate: () => {
      setIsInitializing(false);
      clearError();
    },
    onUpdate: ({ editor }) => {
      try {
        const content = editor.getHTML();
        onContentChange?.(content);

        // Update wizard data if in wizard mode
        if (config.mode === 'wizard' && onWizardDataChange) {
          onWizardDataChange({
            ...wizardData,
            importedContent: content
          });
        }
      } catch (error) {
        handleEngineError({
          code: 'UPDATE_ERROR',
          message: 'Failed to update document content',
          details: error,
          timestamp: new Date()
        });
      }
    }
  });

  // Update editor content when initialContent changes (for wizard integration)
  React.useEffect(() => {
    if (editor && initialContent && !isInitializing) {
      const currentContent = editor.getHTML();

      // For wizard mode, be more aggressive about updating content
      // to ensure form changes are reflected immediately
      const shouldUpdate = config.mode === 'wizard'
        ? currentContent !== initialContent
        : currentContent !== initialContent && !currentContent.includes(initialContent.substring(0, 100));

      if (shouldUpdate) {
        console.log('DocumentEngine: Updating editor content from props:', {
          currentLength: currentContent.length,
          newLength: initialContent.length,
          wizardMode: config.mode === 'wizard',
          currentPreview: currentContent.substring(0, 100) + '...',
          newPreview: initialContent.substring(0, 100) + '...'
        });

        // Preserve cursor position if possible
        const { from } = editor.state.selection;
        editor.commands.setContent(initialContent);

        // Try to restore cursor position
        try {
          const newSize = editor.state.doc.content.size;
          if (from <= newSize) {
            const safePosition = Math.min(from, newSize);
            editor.commands.setTextSelection({ from: safePosition, to: safePosition });
          }
        } catch (e) {
          // Cursor restoration failed, but that's okay
          console.log('DocumentEngine: Could not restore cursor position');
        }
      }
    }
  }, [editor, initialContent, isInitializing, config.mode]);

  // Use document engine hook for advanced functionality
  const documentEngine = useDocumentEngine({
    editor,
    config,
    onError
  });

  // Handle export functionality
  const handleExport = useCallback(async (options: ExportOptions) => {
    if (!editor) return;

    setIsExporting(true);
    setOperationInProgress({ type: 'exporting', progress: 0 });

    try {
      // Simulate progress for better UX
      setOperationInProgress({ type: 'exporting', progress: 25 });

      const exporter = new DocumentExporter(editor, appliedTheme);

      setOperationInProgress({ type: 'exporting', progress: 75 });

      const blob = await exporter.export(options);

      setOperationInProgress({ type: 'exporting', progress: 100 });

      onExport?.(options.format, blob);
    } catch (error) {
      const exportError = {
        code: 'EXPORT_ERROR',
        message: 'Failed to export document',
        details: error,
        timestamp: new Date()
      };

      handleEngineError(exportError);
      onError?.(exportError);
    } finally {
      setIsExporting(false);
      setOperationInProgress({ type: null });
    }
  }, [editor, appliedTheme, onExport, onError, handleEngineError]);

  // Handle save functionality
  const handleSave = useCallback(async () => {
    if (!editor) return;

    setOperationInProgress({ type: 'saving', progress: 0 });

    try {
      setOperationInProgress({ type: 'saving', progress: 50 });

      const documentData: DocumentData = {
        id: documentEngine.structure.metadata.id,
        content: editor.getHTML(),
        structure: documentEngine.structure,
        metadata: documentEngine.structure.metadata,
        version: documentEngine.structure.metadata.version
      };

      await onSave?.(documentData);

      setOperationInProgress({ type: 'saving', progress: 100 });

      // Clear progress after a brief delay
      setTimeout(() => {
        setOperationInProgress({ type: null });
      }, 500);
    } catch (error) {
      const saveError = {
        code: 'SAVE_ERROR',
        message: 'Failed to save document',
        details: error,
        timestamp: new Date()
      };

      handleEngineError(saveError);
      onError?.(saveError);
      setOperationInProgress({ type: null });
    }
  }, [editor, documentEngine.structure, onSave, onError, handleEngineError]);

  // Render toolbar based on configuration
  const renderToolbar = () => {
    if (config.toolbar?.simplified === false || readOnly) {
      return null;
    }

    return (
      <DocumentToolbar
        editor={editor}
        config={config.toolbar}
        theme={appliedTheme}
        onExport={handleExport}
        onSave={handleSave}
        isExporting={isExporting}
        isLoading={documentEngine.isLoading}
      />
    );
  };

  // Render collaboration features (placeholder for future)
  const renderCollaboration = () => {
    if (!config.collaboration?.enabled) {
      return null;
    }

    return (
      <CollaborationManager
        editor={editor}
        config={config.collaboration}
        permissions={config.permissions}
        events={collaborationEvents}
      />
    );
  };

  // Show loading state while initializing
  if (!editor || isInitializing) {
    return (
      <DocumentEngineLoadingState
        type="initial"
        message="Initializing document engine..."
        className={className}
      />
    );
  }

  // Show operation-specific loading states
  if (operationInProgress.type) {
    return (
      <DocumentEngineLoadingState
        type={operationInProgress.type}
        progress={operationInProgress.progress}
        className={className}
      />
    );
  }

  return (
    <DocumentEngineErrorBoundary
      onError={(error) => {
        handleEngineError(error);
        onError?.(error);
      }}
    >
      <div
        ref={containerRef}
        className={`document-engine ${className}`}
        data-mode={config.mode}
      >
        {/* Toolbar */}
        {renderToolbar()}

        {/* Main Editor Area */}
        <div className="document-editor-container flex-1 bg-gray-50 flex justify-center relative overflow-hidden">
          <div className="w-full h-full overflow-y-auto overflow-x-hidden">
            <EditorContent
              editor={editor}
              className="document-editor-wrapper min-h-full focus:outline-none"
            />
          </div>
        </div>

        {/* Collaboration Features */}
        {renderCollaboration()}

        {/* Enhanced Status Bar */}
        <div className="document-status-bar text-xs text-slate-500 px-4 py-2 border-t border-slate-200 dark:border-slate-700 bg-white dark:bg-slate-950">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              {/* Sync Status */}
              {documentEngine.isLoading ? (
                <span className="flex items-center gap-1 text-blue-500">
                  <div className="w-2 h-2 bg-slate-600 rounded-full animate-pulse" />
                  Syncing...
                </span>
              ) : (
                <span className="flex items-center gap-1 text-green-500">
                  <div className="w-2 h-2 bg-green-500 rounded-full" />
                  Ready
                </span>
              )}

              {/* Error Status */}
              {(documentEngine.error || engineError) && (
                <span className="flex items-center gap-1 text-red-500">
                  <div className="w-2 h-2 bg-red-500 rounded-full" />
                  Error: {(documentEngine.error || engineError)?.message}
                </span>
              )}
            </div>

            {/* Document Stats */}
            <div className="flex items-center gap-4 text-slate-400">
              <span>Mode: {config.mode}</span>
              {editor && (
                <>
                  <span>Words: {editor.storage.characterCount?.words() || 0}</span>
                  <span>Characters: {editor.storage.characterCount?.characters() || 0}</span>
                </>
              )}
            </div>
          </div>
        </div>
      </div>
    </DocumentEngineErrorBoundary>
  );
};

export default DocumentEngine;
