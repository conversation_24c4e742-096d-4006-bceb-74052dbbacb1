import React, { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Bell, Check, Key, Lock, Loader2, Save, User } from "lucide-react";
import {
  ProfileSettings as ProfileSettingsType,
  AccountSettings as AccountSettingsType,
  NotificationSettings as NotificationSettingsType,
  SecuritySettings as SecuritySettingsType
} from "@/lib/user-settings";
import { useClerkProfile } from "@/lib/use-clerk-profile";
import { useClerkSessions, formatLastActive } from "@/lib/use-clerk-sessions";
import { useClerkPassword } from "@/lib/use-clerk-password";

const SettingsPage = () => {
  const [activeTab, setActiveTab] = useState("profile");

  return (
    <div className="w-full h-full bg-background p-4 overflow-auto">
      <Tabs
        value={activeTab}
        onValueChange={setActiveTab}
        className="w-full"
      >
        <TabsList className="grid grid-cols-4 w-full max-w-4xl mb-4">
          <TabsTrigger
            value="profile"
            className="flex items-center"
          >
            <User className="h-4 w-4 mr-2" />
            Profile
          </TabsTrigger>
          <TabsTrigger
            value="account"
            className="flex items-center"
          >
            <Key className="h-4 w-4 mr-2" />
            Account
          </TabsTrigger>
          <TabsTrigger
            value="notifications"
            className="flex items-center"
          >
            <Bell className="h-4 w-4 mr-2" />
            Notifications
          </TabsTrigger>
          <TabsTrigger
            value="security"
            className="flex items-center"
          >
            <Lock className="h-4 w-4 mr-2" />
            Security
          </TabsTrigger>
        </TabsList>

        <TabsContent value="profile" className="mt-0">
          <ProfileSettings />
        </TabsContent>

        <TabsContent value="account" className="mt-0">
          <AccountSettings />
        </TabsContent>

        <TabsContent value="notifications" className="mt-0">
          <NotificationSettings />
        </TabsContent>

        <TabsContent value="security" className="mt-0">
          <SecuritySettings />
        </TabsContent>
      </Tabs>
    </div>
  );
};

const ProfileSettings = () => {
  // Use our new Clerk profile hook instead of the mock data
  const { profileData, isLoading, error, success, saveProfile } = useClerkProfile();
  const [formData, setFormData] = useState<ProfileSettingsType>({
    firstName: '',
    lastName: '',
    email: '',
    title: '',
    company: '',
    timezone: '',
    bio: '',
  });

  // Update form data when profile data changes
  useEffect(() => {
    if (profileData) {
      setFormData({
        firstName: profileData.firstName,
        lastName: profileData.lastName,
        email: profileData.email,
        title: profileData.title,
        company: profileData.company,
        timezone: profileData.timezone,
        bio: profileData.bio,
      });
    }
  }, [profileData]);

  const handleChange = (field: keyof ProfileSettingsType, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    await saveProfile({
      ...profileData,
      firstName: formData.firstName,
      lastName: formData.lastName,
      title: formData.title,
      company: formData.company,
      timezone: formData.timezone,
      bio: formData.bio,
    });
  };

  return (
    <div className="max-w-4xl">
      <form onSubmit={handleSubmit}>
        <div className="flex items-center gap-3 mb-4">
          <div className="h-12 w-12 rounded-full bg-muted flex items-center justify-center text-sm font-medium">
            {formData.firstName.charAt(0)}{formData.lastName.charAt(0)}
          </div>
          <div>
            <h2 className="text-base font-bold">{formData.firstName} {formData.lastName}</h2>
            <p className="text-xs text-muted-foreground">{formData.title}</p>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4">
          <div className="space-y-1.5">
            <Label htmlFor="firstName">First Name</Label>
            <Input
              id="firstName"
              value={formData.firstName}
              onChange={(e) => handleChange('firstName', e.target.value)}
            />
          </div>
          <div className="space-y-1.5">
            <Label htmlFor="lastName">Last Name</Label>
            <Input
              id="lastName"
              value={formData.lastName}
              onChange={(e) => handleChange('lastName', e.target.value)}
            />
          </div>

          <div className="space-y-1.5">
            <Label htmlFor="email">Email</Label>
            <Input
              id="email"
              type="email"
              value={formData.email}
              onChange={(e) => handleChange('email', e.target.value)}
            />
          </div>

          <div className="space-y-1.5">
            <Label htmlFor="title">Job Title</Label>
            <Input
              id="title"
              value={formData.title}
              onChange={(e) => handleChange('title', e.target.value)}
            />
          </div>

          <div className="space-y-1.5">
            <Label htmlFor="company">Company</Label>
            <Input
              id="company"
              value={formData.company}
              onChange={(e) => handleChange('company', e.target.value)}
            />
          </div>

          <div className="space-y-1.5">
            <Label htmlFor="workspace">Workspace</Label>
            <Select defaultValue="legal">
              <SelectTrigger id="workspace">
                <SelectValue placeholder="Select workspace" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="legal">Legal</SelectItem>
                <SelectItem value="finance">Finance</SelectItem>
                <SelectItem value="hr">Human Resources</SelectItem>
                <SelectItem value="it">IT</SelectItem>
                <SelectItem value="marketing">Marketing</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-1.5">
            <Label htmlFor="timezone">Timezone</Label>
            <Select
              value={formData.timezone}
              onValueChange={(value) => handleChange('timezone', value)}
            >
              <SelectTrigger id="timezone">
                <SelectValue placeholder="Select timezone" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="america_new_york">Eastern Time (ET)</SelectItem>
                <SelectItem value="america_chicago">Central Time (CT)</SelectItem>
                <SelectItem value="america_denver">Mountain Time (MT)</SelectItem>
                <SelectItem value="america_los_angeles">Pacific Time (PT)</SelectItem>
                <SelectItem value="europe_london">London (GMT)</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="col-span-2 space-y-1.5">
            <Label htmlFor="bio">Bio</Label>
            <Textarea
              id="bio"
              placeholder="Tell us about yourself"
              value={formData.bio}
              onChange={(e) => handleChange('bio', e.target.value)}
              rows={3}
            />
          </div>
        </div>

        {error && (
          <div className="mt-4 p-2 bg-destructive/10 text-destructive rounded-md text-sm">
            {error}
          </div>
        )}

        {success && (
          <div className="mt-4 p-2 bg-green-100 text-green-800 rounded-md text-sm flex items-center">
            <Check className="h-4 w-4 mr-2" />
            Profile settings saved successfully!
          </div>
        )}

        <div className="mt-6 flex justify-end">
          <Button type="submit" disabled={isLoading}>
            {isLoading ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Saving...
              </>
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                Save Changes
              </>
            )}
          </Button>
        </div>
      </form>
    </div>
  );
};

const AccountSettings = () => {
  const { profileData } = useClerkProfile();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [formData, setFormData] = useState<AccountSettingsType>({
    username: '',
    language: 'en',
  });

  // Update form data when profile data changes
  useEffect(() => {
    if (profileData) {
      setFormData({
        username: profileData.email.split('@')[0], // Use email prefix as username
        language: 'en', // Default language
      });
    }
  }, [profileData]);

  const handleChange = (field: keyof AccountSettingsType, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSave = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);
    setSuccess(false);

    // For now, we're not updating the username through Clerk
    // This would require additional Clerk API calls
    // Just show success message
    setTimeout(() => {
      setSuccess(true);
      setIsLoading(false);
      setTimeout(() => setSuccess(false), 3000);
    }, 500);
  };

  return (
    <div className="max-w-4xl">
      <form onSubmit={handleSave}>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4">
          <div className="space-y-1.5">
            <Label htmlFor="username">Username</Label>
            <Input
              id="username"
              value={formData.username}
              onChange={(e) => handleChange('username', e.target.value)}
            />
          </div>

          <div className="space-y-1.5">
            <Label htmlFor="language">Language</Label>
            <Select
              value={formData.language}
              onValueChange={(value) => handleChange('language', value)}
            >
              <SelectTrigger id="language">
                <SelectValue placeholder="Select language" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="en">English</SelectItem>
                <SelectItem value="es">Spanish</SelectItem>
                <SelectItem value="fr">French</SelectItem>
                <SelectItem value="de">German</SelectItem>
                <SelectItem value="zh">Chinese</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <div className="mt-4">
          <h3 className="text-sm font-medium mb-2">Connected Accounts</h3>
          <div className="space-y-2">
            <div className="flex items-center justify-between border rounded-md p-2">
              <div className="flex items-center gap-2">
                <div className="bg-muted rounded-full p-1">
                  <svg
                    className="h-3 w-3"
                    viewBox="0 0 24 24"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M12 0C5.372 0 0 5.373 0 12C0 18.627 5.372 24 12 24C18.627 24 24 18.627 24 12C24 5.373 18.627 0 12 0ZM19.35 5.55L16.2 8.7C15.6 8.1 14.85 7.8 12 7.8C9.15 7.8 8.4 8.1 7.8 8.7L4.65 5.55C6.3 4.05 8.55 3 12 3C15.45 3 17.7 4.05 19.35 5.55ZM3 12C3 8.55 4.05 6.3 5.55 4.65L8.7 7.8C8.1 8.4 7.8 9.15 7.8 12C7.8 14.85 8.1 15.6 8.7 16.2L5.55 19.35C4.05 17.7 3 15.45 3 12ZM12 21C8.55 21 6.3 19.95 4.65 18.45L7.8 15.3C8.4 15.9 9.15 16.2 12 16.2C14.85 16.2 15.6 15.9 16.2 15.3L19.35 18.45C17.7 19.95 15.45 21 12 21ZM15.3 16.2C15.9 15.6 16.2 14.85 16.2 12C16.2 9.15 15.9 8.4 15.3 7.8L18.45 4.65C19.95 6.3 21 8.55 21 12C21 15.45 19.95 17.7 18.45 19.35L15.3 16.2Z"
                      fill="currentColor"
                    />
                  </svg>
                </div>
                <div>
                  <h4 className="font-medium text-xs">Google</h4>
                  <p className="text-xs text-muted-foreground">Connected</p>
                </div>
              </div>
              <Button variant="outline" size="sm" className="h-7 text-xs">
                Disconnect
              </Button>
            </div>

            <div className="flex items-center justify-between border rounded-md p-2">
              <div className="flex items-center gap-2">
                <div className="bg-muted rounded-full p-1">
                  <svg
                    className="h-3 w-3"
                    viewBox="0 0 24 24"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M22.675 0H1.325C0.593 0 0 0.593 0 1.325V22.676C0 23.407 0.593 24 1.325 24H12.82V14.706H9.692V11.084H12.82V8.413C12.82 5.313 14.713 3.625 17.479 3.625C18.804 3.625 19.942 3.724 20.274 3.768V7.008L18.356 7.009C16.852 7.009 16.561 7.724 16.561 8.772V11.085H20.148L19.681 14.707H16.561V24H22.677C23.407 24 24 23.407 24 22.675V1.325C24 0.593 23.407 0 22.675 0Z"
                      fill="currentColor"
                    />
                  </svg>
                </div>
                <div>
                  <h4 className="font-medium text-xs">Facebook</h4>
                  <p className="text-xs text-muted-foreground">Not connected</p>
                </div>
              </div>
              <Button variant="outline" size="sm" className="h-7 text-xs">
                Connect
              </Button>
            </div>
          </div>
        </div>

        {error && (
          <div className="mt-4 p-2 bg-destructive/10 text-destructive rounded-md text-sm">
            {error}
          </div>
        )}

        {success && (
          <div className="mt-4 p-2 bg-green-100 text-green-800 rounded-md text-sm flex items-center">
            <Check className="h-4 w-4 mr-2" />
            Account settings saved successfully!
          </div>
        )}

        <div className="mt-4 flex justify-end">
          <Button type="submit" size="sm" disabled={isLoading} className="h-8">
            {isLoading ? (
              <>
                <Loader2 className="h-3 w-3 mr-2 animate-spin" />
                Saving...
              </>
            ) : (
              <>
                <Save className="h-3 w-3 mr-2" />
                Save Changes
              </>
            )}
          </Button>
        </div>
      </form>
    </div>
  );
};

const NotificationSettings = () => {
  const { profileData, isLoading, error, success, saveNotificationPreferences } = useClerkProfile();
  const [formData, setFormData] = useState<NotificationSettingsType>({
    emailApprovals: true,
    emailUpdates: true,
    emailReminders: false,
    emailComments: true,
    systemApprovals: true,
    browserNotifications: false,
    emailDigestFrequency: 'daily',
  });

  // Load notification preferences from Clerk profile data
  useEffect(() => {
    if (profileData?.notifications) {
      setFormData({
        emailApprovals: profileData.notifications.emailApprovals,
        emailUpdates: profileData.notifications.emailUpdates,
        emailReminders: profileData.notifications.emailReminders,
        emailComments: profileData.notifications.emailComments,
        systemApprovals: profileData.notifications.systemApprovals,
        browserNotifications: profileData.notifications.browserNotifications,
        emailDigestFrequency: profileData.notifications.emailDigestFrequency,
      });
    }
  }, [profileData]);

  const handleChange = (field: keyof NotificationSettingsType, value: boolean | string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Save notification preferences to Clerk's publicMetadata
    await saveNotificationPreferences({
      emailApprovals: formData.emailApprovals,
      emailUpdates: formData.emailUpdates,
      emailReminders: formData.emailReminders,
      emailComments: formData.emailComments,
      systemApprovals: formData.systemApprovals,
      browserNotifications: formData.browserNotifications,
      emailDigestFrequency: formData.emailDigestFrequency,
    });
  };

  return (
    <div className="max-w-4xl">
      <form onSubmit={handleSubmit}>
        <div className="space-y-6">
          <div>
            <h3 className="text-sm font-medium mb-2">Email Notifications</h3>
            <div className="space-y-2 border rounded-md divide-y">
              <div className="flex items-center justify-between p-2">
                <div>
                  <Label htmlFor="email-approvals" className="font-medium text-xs">
                    Contract Approvals
                  </Label>
                  <p className="text-xs text-muted-foreground">
                    Receive notifications when contracts need your approval
                  </p>
                </div>
                <Switch
                  id="email-approvals"
                  checked={formData.emailApprovals}
                  onCheckedChange={(checked) => handleChange('emailApprovals', checked)}
                />
              </div>

              <div className="flex items-center justify-between p-3">
                <div>
                  <Label htmlFor="email-comments" className="font-medium text-sm">
                    Comments
                  </Label>
                  <p className="text-xs text-muted-foreground">
                    Receive notifications when someone comments on your contracts
                  </p>
                </div>
                <Switch
                  id="email-comments"
                  checked={formData.emailComments}
                  onCheckedChange={(checked) => handleChange('emailComments', checked)}
                />
              </div>

              <div className="flex items-center justify-between p-3">
                <div>
                  <Label htmlFor="email-updates" className="font-medium text-sm">
                    Contract Updates
                  </Label>
                  <p className="text-xs text-muted-foreground">
                    Receive notifications when contracts are updated
                  </p>
                </div>
                <Switch
                  id="email-updates"
                  checked={formData.emailUpdates}
                  onCheckedChange={(checked) => handleChange('emailUpdates', checked)}
                />
              </div>

              <div className="flex items-center justify-between p-3">
                <div>
                  <Label htmlFor="email-expiry" className="font-medium text-sm">
                    Contract Expiry
                  </Label>
                  <p className="text-xs text-muted-foreground">
                    Receive notifications when contracts are about to expire
                  </p>
                </div>
                <Switch
                  id="email-expiry"
                  checked={formData.emailReminders}
                  onCheckedChange={(checked) => handleChange('emailReminders', checked)}
                />
              </div>
            </div>
          </div>

          <div>
            <h3 className="text-base font-medium mb-3">System Notifications</h3>
            <div className="space-y-3 border rounded-md divide-y">
              <div className="flex items-center justify-between p-3">
                <div>
                  <Label htmlFor="system-approvals" className="font-medium text-sm">
                    In-App Notifications
                  </Label>
                  <p className="text-xs text-muted-foreground">
                    Receive notifications within the application
                  </p>
                </div>
                <Switch
                  id="system-approvals"
                  checked={formData.systemApprovals}
                  onCheckedChange={(checked) => handleChange('systemApprovals', checked)}
                />
              </div>

              <div className="flex items-center justify-between p-3">
                <div>
                  <Label htmlFor="browser-notifications" className="font-medium text-sm">
                    Browser Notifications
                  </Label>
                  <p className="text-xs text-muted-foreground">
                    Receive browser push notifications
                  </p>
                </div>
                <Switch
                  id="browser-notifications"
                  checked={formData.browserNotifications}
                  onCheckedChange={(checked) => handleChange('browserNotifications', checked)}
                />
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4">
            <div className="space-y-1.5">
              <Label htmlFor="frequency">Email Digest Frequency</Label>
              <Select
                value={formData.emailDigestFrequency}
                onValueChange={(value) => handleChange('emailDigestFrequency', value)}
              >
                <SelectTrigger id="frequency">
                  <SelectValue placeholder="Select frequency" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="realtime">Real-time</SelectItem>
                  <SelectItem value="daily">Daily Digest</SelectItem>
                  <SelectItem value="weekly">Weekly Digest</SelectItem>
                  <SelectItem value="never">Never</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>

        {error && (
          <div className="mt-4 p-2 bg-destructive/10 text-destructive rounded-md text-sm">
            {error}
          </div>
        )}

        {success && (
          <div className="mt-4 p-2 bg-green-100 text-green-800 rounded-md text-sm flex items-center">
            <Check className="h-4 w-4 mr-2" />
            Notification settings saved successfully!
          </div>
        )}

        <div className="mt-6 flex justify-end">
          <Button type="submit" disabled={isLoading}>
            {isLoading ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Saving...
              </>
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                Save Changes
              </>
            )}
          </Button>
        </div>
      </form>
    </div>
  );
};

const SecuritySettings = () => {
  const { changePassword, isLoading, error, success } = useClerkPassword();
  const [passwordData, setPasswordData] = useState<SecuritySettingsType>({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  });

  const handleChange = (field: keyof SecuritySettingsType, value: string) => {
    setPasswordData(prev => ({ ...prev, [field]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Use our Clerk password hook to change the password
    await changePassword({
      currentPassword: passwordData.currentPassword,
      newPassword: passwordData.newPassword,
      confirmPassword: passwordData.confirmPassword
    });

    // Clear form on success
    if (success) {
      setPasswordData({
        currentPassword: '',
        newPassword: '',
        confirmPassword: ''
      });
    }
  };

  return (
    <div className="max-w-4xl">
      <div className="space-y-6">
        <div>
          <h3 className="text-sm font-medium mb-2">Change Password</h3>
          <form onSubmit={handleSubmit}>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-x-4 gap-y-3">
              <div className="space-y-1">
                <Label htmlFor="current-password" className="text-xs">Current Password</Label>
                <Input
                  id="current-password"
                  type="password"
                  className="h-8"
                  value={passwordData.currentPassword}
                  onChange={(e) => handleChange('currentPassword', e.target.value)}
                  required
                />
              </div>

              <div className="space-y-1.5">
                <Label htmlFor="new-password">New Password</Label>
                <Input
                  id="new-password"
                  type="password"
                  value={passwordData.newPassword}
                  onChange={(e) => handleChange('newPassword', e.target.value)}
                  required
                />
              </div>

              <div className="space-y-1.5">
                <Label htmlFor="confirm-password">Confirm New Password</Label>
                <Input
                  id="confirm-password"
                  type="password"
                  value={passwordData.confirmPassword}
                  onChange={(e) => handleChange('confirmPassword', e.target.value)}
                  required
                />
              </div>
            </div>

            {error && (
              <div className="mt-4 p-2 bg-destructive/10 text-destructive rounded-md text-sm">
                {error}
              </div>
            )}

            {success && (
              <div className="mt-4 p-2 bg-green-100 text-green-800 rounded-md text-sm flex items-center">
                <Check className="h-4 w-4 mr-2" />
                Password updated successfully!
              </div>
            )}

            <Button type="submit" className="mt-4" disabled={isLoading}>
              {isLoading ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Updating...
                </>
              ) : (
                "Update Password"
              )}
            </Button>
          </form>
        </div>

        <div>
          <h3 className="text-base font-medium mb-3">Two-Factor Authentication</h3>
          <div className="border rounded-md p-3">
            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="2fa" className="font-medium text-sm">
                  Enable Two-Factor Authentication
                </Label>
                <p className="text-xs text-muted-foreground">
                  Add an extra layer of security to your account
                </p>
              </div>
              <Switch id="2fa" />
            </div>
          </div>
        </div>

        <div>
          <h3 className="text-base font-medium mb-3">Session Management</h3>
          <SessionManagement />
        </div>
      </div>
    </div>
  );
};



const SessionManagement = () => {
  const { sessions, isLoading, error, success, revokeSession, revokeAllOtherSessions } = useClerkSessions();

  return (
    <div className="space-y-4">
      {isLoading ? (
        <div className="flex justify-center p-4">
          <Loader2 className="h-6 w-6 animate-spin text-muted-foreground" />
        </div>
      ) : error ? (
        <div className="p-2 bg-destructive/10 text-destructive rounded-md text-sm">
          {error}
        </div>
      ) : (
        <>
          <div className="space-y-3">
            {sessions.map((session) => (
              <div
                key={session.id}
                className={`border rounded-md p-3 ${session.isCurrent ? 'bg-muted/20' : ''}`}
              >
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium text-sm">
                      {session.browser} on {session.os}
                      {session.deviceType !== 'Desktop' && ` (${session.deviceType})`}
                    </h4>
                    <p className="text-xs text-muted-foreground">
                      {session.location} • {session.isCurrent ? 'Active now' : `Last active ${formatLastActive(session.lastActiveAt)}`}
                    </p>
                  </div>
                  {session.isCurrent ? (
                    <Badge variant="outline">Current</Badge>
                  ) : (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => revokeSession(session.id)}
                      disabled={isLoading}
                    >
                      {isLoading ? (
                        <Loader2 className="h-3 w-3 animate-spin" />
                      ) : (
                        'Logout'
                      )}
                    </Button>
                  )}
                </div>
              </div>
            ))}
          </div>

          {success && (
            <div className="mt-4 p-2 bg-green-100 text-green-800 rounded-md text-sm flex items-center">
              <Check className="h-4 w-4 mr-2" />
              Session ended successfully!
            </div>
          )}

          <div className="mt-4 flex justify-end">
            <Button
              variant="destructive"
              size="sm"
              onClick={revokeAllOtherSessions}
              disabled={isLoading || sessions.filter(s => !s.isCurrent).length === 0}
            >
              {isLoading ? (
                <Loader2 className="h-3 w-3 mr-2 animate-spin" />
              ) : null}
              Logout of All Other Devices
            </Button>
          </div>
        </>
      )}
    </div>
  );
};

export default SettingsPage;
